{"actions": [], "allow_rename": 1, "creation": "2024-09-13 14:41:07.161668", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["default_price_list", "column_break_8yjq8", "enable_signature_validation", "gate_pass_expiry_hours", "corridor_levy_countries_section", "countries", "service_pricing_criteria_section", "service_types", "lcl_service_pricing_criteria_section", "loose_types", "no_of_days_for_container_storage_charges_section", "storage_days", "example"], "fields": [{"default": "Standard Selling", "fieldname": "default_price_list", "fieldtype": "Link", "in_list_view": 1, "label": "Default Price List", "options": "Price List", "reqd": 1}, {"fieldname": "no_of_days_for_container_storage_charges_section", "fieldtype": "Section Break", "label": "No of Days for Container Storage Charges"}, {"fieldname": "storage_days", "fieldtype": "Table", "label": "Storage Days", "options": "ICD TZ Settings Detail"}, {"fieldname": "example", "fieldtype": "HTML", "options": "<h2>Example</h2>\n<style>\ntable {\n  font-family: arial, sans-serif;\n  border-collapse: collapse;\n  width: 100%;\n}\n\ntd, th {\n  border: 1px solid #dddddd;\n  text-align: left;\n  padding: 8px;\n}\n\n</style>\n\n<table>\n  <tbody>\n  <tr style=\"background-color: #dddddd;\">\n    <th>Destination</th>\n    <th>Charge</th>\n    <th>From</th>\n    <th>To</th>\n  </tr>\n  <tr>\n    <td>Local</td>\n    <td>Free</td>\n    <td>1</td>\n    <td>7</td>\n  </tr>\n  <tr>\n    <td>Local</td>\n    <td>Single</td>\n    <td>8</td>\n    <td>14</td>\n  </tr>\n  <tr>\n    <td>Local</td>\n    <td>Double</td>\n    <td>15</td>\n    <td>9999999</td>\n  </tr>\n  <tr>\n    <td>DRC</td>\n    <td>Free</td>\n    <td>1</td>\n    <td>30</td>\n  </tr>\n  <tr>\n    <td>DRC</td>\n    <td>Single</td>\n    <td>31</td>\n    <td>37</td>\n  </tr>\n  <tr>\n    <td>DRC</td>\n    <td>Double</td>\n    <td>38</td>\n    <td>9999999</td>\n  </tr>\n  <tr>\n    <td>Other</td>\n    <td>Free</td>\n    <td>1</td>\n    <td>14</td>\n  </tr>\n  <tr>\n    <td>Other</td>\n    <td>Single</td>\n    <td>15</td>\n    <td>21</td>\n  </tr>\n  <tr>\n    <td>Other</td>\n    <td>Double</td>\n    <td>22</td>\n    <td>9999999</td>\n  </tr>\n</tbody></table>\n"}, {"fieldname": "column_break_8yjq8", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "enable_signature_validation", "fieldtype": "Check", "label": "Enable Signature Validation"}, {"fieldname": "corridor_levy_countries_section", "fieldtype": "Section Break", "label": "Corridor Levy Countries"}, {"description": "Countries eligible to pay Corridor Levy", "fieldname": "countries", "fieldtype": "Table MultiSelect", "label": "countries", "options": "Corridor Levy Country"}, {"fieldname": "service_pricing_criteria_section", "fieldtype": "Section Break", "label": "FCL Service Pricing Criteria"}, {"fieldname": "service_types", "fieldtype": "Table", "label": "Criteria", "options": "ICD TZ Service Detail"}, {"fieldname": "lcl_service_pricing_criteria_section", "fieldtype": "Section Break", "label": "LCL Service Pricing Criteria"}, {"fieldname": "loose_types", "fieldtype": "Table", "label": "Criteria", "options": "ICD TZ Loose Detail"}, {"description": "eg: 12, 24, 48, 72 etc", "fieldname": "gate_pass_expiry_hours", "fieldtype": "Int", "label": "Gate Pass Expiry Hours", "reqd": 1}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-09-14 15:12:14.287641", "modified_by": "Administrator", "module": "Icd Tz", "name": "ICD TZ Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}