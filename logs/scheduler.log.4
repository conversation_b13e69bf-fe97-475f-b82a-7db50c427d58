2025-09-12 08:26:00,866 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:26:00,870 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-09-12 08:26:00,875 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-09-12 08:26:00,879 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for rubis
2025-09-12 08:26:00,883 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:26:00,886 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rubis
2025-09-12 08:26:00,890 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:26:00,893 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:26:00,897 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rubis
2025-09-12 08:26:00,900 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rubis
2025-09-12 08:26:00,905 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:26:00,910 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rubis
2025-09-12 08:26:00,913 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:26:00,916 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-09-12 08:26:00,922 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rubis
2025-09-12 08:26:00,929 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-09-12 08:26:00,933 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for rubis
2025-09-12 08:26:00,936 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:26:00,939 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for rubis
2025-09-12 08:26:00,942 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:26:00,945 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-09-12 08:26:00,947 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rubis
2025-09-12 08:26:00,950 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rubis
2025-09-12 08:26:00,952 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rubis
2025-09-12 08:26:00,954 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:26:00,956 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-09-12 08:26:00,958 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-09-12 08:26:00,961 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rubis
2025-09-12 08:26:00,963 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:26:00,966 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:26:00,968 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:26:00,970 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:26:00,973 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rubis
2025-09-12 08:26:00,975 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rubis
2025-09-12 08:26:00,977 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-09-12 08:26:00,979 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-09-12 08:26:00,984 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:26:00,987 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rubis
2025-09-12 08:26:00,990 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:26:00,992 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rubis
2025-09-12 08:26:00,994 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-09-12 08:26:00,998 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:26:01,000 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-09-12 08:26:01,002 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for rubis
2025-09-12 08:26:01,006 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:26:01,008 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rubis
2025-09-12 08:26:01,010 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:26:01,012 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rubis
2025-09-12 08:26:01,014 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-09-12 08:26:01,017 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-09-12 08:26:01,019 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:26:01,020 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-09-12 08:26:01,022 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for rubis
2025-09-12 08:26:01,023 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-09-12 08:26:01,026 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rubis
2025-09-12 08:26:01,027 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-09-12 08:26:01,029 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:26:01,030 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:26:01,032 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:26:01,034 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:26:01,036 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rubis
2025-09-12 08:26:01,038 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:26:01,041 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:26:01,043 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rubis
2025-09-12 08:26:01,045 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:26:01,047 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:26:01,048 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:26:01,050 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rubis
2025-09-12 08:26:01,052 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rubis
2025-09-12 08:26:01,055 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:26:01,056 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-09-12 08:26:01,058 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:26:01,060 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-09-12 08:26:01,062 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:26:01,064 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rubis
2025-09-12 08:26:01,067 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-09-12 08:26:01,069 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:26:01,073 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:26:01,075 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rubis
2025-09-12 08:26:01,077 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:26:01,079 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-09-12 08:26:01,083 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:26:01,086 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-09-12 08:26:01,088 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rubis
2025-09-12 08:26:01,091 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:26:01,093 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-09-12 08:26:01,095 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rubis
2025-09-12 08:26:01,098 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-09-12 08:26:01,100 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:26:01,102 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:26:01,104 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rubis
2025-09-12 08:26:01,106 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:26:01,108 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:26:01,110 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for rubis
2025-09-12 08:26:01,112 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:26:01,114 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:26:01,117 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:26:01,120 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-09-12 08:26:01,122 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:26:01,124 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rubis
2025-09-12 08:27:01,546 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rubis
2025-09-12 08:27:01,549 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:27:01,550 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-09-12 08:27:01,552 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:27:01,554 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for rubis
2025-09-12 08:27:01,556 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-09-12 08:27:01,557 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rubis
2025-09-12 08:27:01,559 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rubis
2025-09-12 08:27:01,561 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:27:01,562 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-09-12 08:27:01,565 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for rubis
2025-09-12 08:27:01,566 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-09-12 08:27:01,568 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rubis
2025-09-12 08:27:01,572 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-09-12 08:27:01,574 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:27:01,575 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:27:01,577 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rubis
2025-09-12 08:27:01,579 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rubis
2025-09-12 08:27:01,580 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rubis
2025-09-12 08:27:01,582 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-09-12 08:27:01,584 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:27:01,586 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:27:01,590 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:27:01,592 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:27:01,593 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-09-12 08:27:01,595 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-09-12 08:27:01,597 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:27:01,598 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rubis
2025-09-12 08:27:01,600 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for rubis
2025-09-12 08:27:01,602 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:27:01,605 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:27:01,607 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-09-12 08:27:01,608 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-09-12 08:27:01,610 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-09-12 08:27:01,612 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:27:01,613 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-09-12 08:27:01,615 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-09-12 08:27:01,616 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:27:01,618 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rubis
2025-09-12 08:27:01,620 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rubis
2025-09-12 08:27:01,622 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:27:01,623 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:27:01,625 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rubis
2025-09-12 08:27:01,627 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:27:01,629 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rubis
2025-09-12 08:27:01,631 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:27:01,633 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:27:01,636 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-09-12 08:27:01,638 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:27:01,639 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rubis
2025-09-12 08:27:01,640 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-09-12 08:27:01,642 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:27:01,643 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-09-12 08:27:01,646 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:27:01,648 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:27:01,649 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rubis
2025-09-12 08:27:01,651 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:27:01,653 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:27:01,655 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for rubis
2025-09-12 08:27:01,656 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rubis
2025-09-12 08:27:01,658 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:27:01,660 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rubis
2025-09-12 08:27:01,662 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:27:01,664 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rubis
2025-09-12 08:27:01,666 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rubis
2025-09-12 08:27:01,669 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:27:01,672 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:27:01,674 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:27:01,676 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:27:01,678 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rubis
2025-09-12 08:27:01,681 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rubis
2025-09-12 08:27:01,684 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:27:01,686 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:27:01,688 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-09-12 08:27:01,691 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-09-12 08:27:01,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:27:01,695 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for rubis
2025-09-12 08:27:01,697 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-09-12 08:27:01,699 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-09-12 08:27:01,700 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:27:01,703 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rubis
2025-09-12 08:27:01,705 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:27:01,707 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:27:01,709 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rubis
2025-09-12 08:27:01,711 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for rubis
2025-09-12 08:27:01,713 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rubis
2025-09-12 08:27:01,715 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:27:01,716 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:27:01,718 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:27:01,721 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:27:01,723 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:27:01,727 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-09-12 08:27:01,729 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:27:01,731 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rubis
2025-09-12 08:27:01,733 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-09-12 08:27:01,736 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rubis
2025-09-12 08:27:01,738 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-09-12 08:27:01,740 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rubis
2025-09-12 08:27:01,742 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:27:01,744 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-09-12 08:28:02,160 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:28:02,174 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:28:02,176 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:28:02,179 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:28:02,187 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:28:02,190 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:28:02,200 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:28:02,202 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:28:02,210 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:28:02,212 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:28:02,218 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:28:02,220 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:28:02,224 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:28:02,228 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:28:02,229 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:28:02,232 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:28:02,242 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:28:02,244 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:28:02,255 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:28:02,261 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:28:02,264 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:28:02,266 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:28:02,271 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:28:02,279 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:28:02,281 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:28:02,286 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:28:02,290 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:28:02,293 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:28:02,304 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:28:02,310 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:28:02,317 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:28:02,319 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:29:02,341 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:29:02,345 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:29:02,348 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:29:02,354 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:29:02,357 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:29:02,359 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:29:02,360 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:29:02,369 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:29:02,380 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:29:02,384 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:29:02,388 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:29:02,393 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:29:02,395 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:29:02,396 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:29:02,398 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:29:02,399 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:29:02,403 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:29:02,409 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:29:02,413 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:29:02,422 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:29:02,424 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:29:02,428 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:29:02,430 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:29:02,434 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:29:02,442 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:29:02,454 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:29:02,461 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:29:02,469 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:29:02,472 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:29:02,476 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:29:02,479 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:29:02,481 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:30:02,947 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:30:02,949 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:30:02,952 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:30:02,954 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:30:02,956 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:30:02,966 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:30:02,968 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:30:02,981 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:30:02,982 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:30:02,988 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:30:02,992 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:30:03,006 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:30:03,014 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:30:03,015 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:30:03,023 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:30:03,031 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:30:03,033 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:30:03,039 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:30:03,050 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:30:03,052 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:30:03,060 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:30:03,061 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:30:03,063 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:30:03,069 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:30:03,074 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:30:03,085 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:30:03,090 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:30:03,093 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:30:03,104 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:30:03,111 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:30:03,114 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:30:03,119 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:31:03,760 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-09-12 08:31:03,762 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:31:03,764 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-09-12 08:31:03,766 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:31:03,770 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-09-12 08:31:03,771 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-09-12 08:31:03,773 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:31:03,775 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-09-12 08:31:03,778 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-09-12 08:31:03,779 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:31:03,781 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:31:03,783 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:31:03,785 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-09-12 08:31:03,787 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:31:03,792 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:31:03,798 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:31:03,802 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-09-12 08:31:03,804 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:31:03,806 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:31:03,811 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-09-12 08:31:03,815 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:31:03,820 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:31:03,823 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:31:03,827 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:31:03,829 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:31:03,834 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:31:03,837 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:31:03,839 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:31:03,843 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-09-12 08:31:03,851 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:31:03,854 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:31:03,858 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-09-12 08:31:03,861 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:31:03,863 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:31:03,865 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-09-12 08:31:03,867 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:31:03,879 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-09-12 08:31:03,882 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:31:03,884 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-09-12 08:31:03,888 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-09-12 08:31:03,890 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:31:03,892 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:31:03,894 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:31:03,899 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:31:03,901 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-09-12 08:31:03,910 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:31:03,918 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:31:03,922 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:32:04,250 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:32:04,251 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:32:04,253 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:32:04,256 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:32:04,258 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:32:04,264 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:32:04,268 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:32:04,275 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:32:04,278 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:32:04,285 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:32:04,288 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:32:04,290 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:32:04,295 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:32:04,299 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:32:04,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:32:04,304 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:32:04,306 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:32:04,310 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:32:04,313 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:32:04,327 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:32:04,328 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:32:04,333 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:32:04,335 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:32:04,340 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:32:04,341 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:32:04,342 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:32:04,349 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:32:04,350 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:32:04,351 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:32:04,353 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:32:04,358 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:32:04,363 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:33:04,529 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:33:04,533 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:33:04,536 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:33:04,539 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:33:04,542 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:33:04,546 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:33:04,548 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:33:04,551 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:33:04,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:33:04,557 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:33:04,560 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:33:04,568 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:33:04,576 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:33:04,585 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:33:04,587 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:33:04,591 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:33:04,592 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:33:04,594 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:33:04,596 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:33:04,597 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:33:04,598 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:33:04,600 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:33:04,602 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:33:04,613 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:33:04,620 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:33:04,622 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:33:04,623 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:33:04,626 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:33:04,630 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:33:04,633 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:33:04,637 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:33:04,640 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:34:04,924 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:34:04,930 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:34:04,933 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:34:04,936 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:34:04,938 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:34:04,942 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:34:04,946 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:34:04,951 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:34:04,954 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:34:04,956 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:34:04,964 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:34:04,966 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:34:04,971 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:34:04,972 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:34:04,981 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:34:04,983 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:34:04,985 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:34:04,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:34:04,994 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:34:05,000 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:34:05,006 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:34:05,011 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:34:05,014 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:34:05,016 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:34:05,019 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:34:05,021 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:34:05,032 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:34:05,035 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:34:05,038 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:34:05,040 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:34:05,046 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:34:05,049 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:35:05,415 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:35:05,419 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:35:05,427 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:35:05,429 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:35:05,432 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:35:05,435 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:35:05,440 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:35:05,445 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:35:05,451 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:35:05,461 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:35:05,464 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:35:05,480 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:35:05,487 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:35:05,491 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:35:05,492 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:35:05,494 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:35:05,502 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:35:05,504 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:35:05,506 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:35:05,508 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:35:05,511 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:35:05,514 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:35:05,518 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:35:05,522 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:35:05,524 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:35:05,526 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:35:05,527 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:35:05,534 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:35:05,538 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:35:05,542 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:35:05,546 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:35:05,547 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:36:06,015 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:36:06,019 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:36:06,021 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:36:06,027 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:36:06,034 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:36:06,038 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:36:06,068 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:36:06,078 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:36:06,087 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:36:06,091 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:36:06,093 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:36:06,097 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:36:06,105 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:36:06,107 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:36:06,111 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:36:06,117 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:36:06,118 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:36:06,120 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:36:06,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:36:06,140 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:36:06,141 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:36:06,143 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:36:06,146 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:36:06,149 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:36:06,154 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:36:06,161 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:36:06,162 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:36:06,165 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:36:06,168 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:36:06,177 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:36:06,178 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:36:06,180 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:37:06,396 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:37:06,404 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:37:06,414 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:37:06,423 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:37:06,425 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:37:06,429 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:37:06,431 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:37:06,434 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:37:06,436 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:37:06,440 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:37:06,442 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:37:06,445 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:37:06,447 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:37:06,451 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:37:06,457 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:37:06,465 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:37:06,467 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:37:06,469 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:37:06,471 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:37:06,474 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:37:06,478 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:37:06,481 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:37:06,484 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:37:06,486 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:37:06,489 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:37:06,494 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:37:06,498 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:37:06,506 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:37:06,508 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:37:06,520 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:37:06,525 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:37:06,531 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:37:06,534 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:37:06,540 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:37:06,541 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:37:06,544 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:37:06,545 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:37:06,550 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:38:06,705 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:38:06,711 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:38:06,718 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:38:06,727 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:38:06,729 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:38:06,732 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:38:06,734 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:38:06,738 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:38:06,740 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:38:06,741 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:38:06,743 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:38:06,744 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:38:06,745 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:38:06,755 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:38:06,757 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:38:06,759 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:38:06,761 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:38:06,768 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:38:06,769 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:38:06,774 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:38:06,775 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:38:06,778 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:38:06,781 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:38:06,784 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:38:06,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:38:06,797 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:38:06,800 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:38:06,802 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:38:06,804 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:38:06,805 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:38:06,807 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:38:06,814 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:38:06,825 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:38:06,828 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:38:06,834 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:38:06,836 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:38:06,840 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:38:06,841 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:39:07,308 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:39:07,310 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:39:07,315 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:39:07,317 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:39:07,319 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:39:07,321 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:39:07,322 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:39:07,332 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:39:07,333 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:39:07,338 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:39:07,339 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:39:07,343 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:39:07,346 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:39:07,347 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:39:07,349 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:39:07,353 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:39:07,363 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:39:07,369 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:39:07,371 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:39:07,377 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:39:07,379 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:39:07,381 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:39:07,389 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:39:07,393 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:39:07,395 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:39:07,399 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:39:07,401 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:39:07,405 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:39:07,410 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:39:07,412 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
