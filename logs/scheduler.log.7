2025-09-11 13:01:05,308 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 13:01:05,312 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 13:01:05,325 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 13:01:05,335 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 13:01:05,344 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 13:01:05,355 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 13:01:05,377 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 13:01:05,382 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 13:01:05,386 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 13:01:05,387 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 13:01:05,391 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 13:01:05,398 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 13:01:05,404 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 13:01:05,406 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:01:28,900 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:01:28,902 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:01:28,920 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:01:28,921 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:01:28,929 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:01:28,934 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:01:28,938 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:01:28,942 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:01:28,956 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:01:28,964 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:01:28,968 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:01:28,974 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:01:28,975 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:01:28,985 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:02:29,471 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:02:29,477 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:02:29,483 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:02:29,490 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:02:29,497 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:02:29,500 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:02:29,501 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:02:29,509 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:02:29,515 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:02:29,533 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:02:29,538 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:02:29,543 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:02:29,549 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:02:29,556 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:03:29,726 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:03:29,732 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:03:29,734 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:03:29,753 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:03:29,756 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:03:29,758 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:03:29,772 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:03:29,780 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:03:29,785 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:03:29,805 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:03:29,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:03:29,813 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:03:29,817 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:03:29,829 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:05:30,689 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 14:05:30,690 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 14:05:30,694 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 14:05:30,702 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 14:05:30,732 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 14:05:30,735 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 15:01:53,513 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:01:53,518 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-11 15:01:53,520 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-11 15:01:53,524 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:01:53,526 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-11 15:01:53,536 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 15:01:53,538 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-11 15:01:53,539 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:01:53,547 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-11 15:01:53,551 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:01:53,552 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-11 15:01:53,554 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-11 15:01:53,557 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-11 15:01:53,560 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-11 15:01:53,564 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for working
2025-09-11 15:01:53,566 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-11 15:01:53,568 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-11 15:01:53,571 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 15:01:53,574 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-11 15:01:53,576 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:01:53,578 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 15:01:53,582 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:01:53,584 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:01:53,586 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:01:53,591 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-11 15:01:53,593 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 15:01:53,595 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-11 15:01:53,601 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 15:01:53,607 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 15:01:53,608 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:01:53,609 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:01:53,611 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for working
2025-09-11 15:01:53,612 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:01:53,616 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-11 15:01:53,620 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-11 15:01:53,624 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-11 15:01:53,630 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-11 15:01:53,632 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 15:01:53,634 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:01:53,635 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:01:53,639 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-11 15:02:53,936 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:02:53,938 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:02:53,942 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:02:53,948 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:02:53,961 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:02:53,965 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:02:53,967 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:02:53,973 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:02:53,986 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:02:54,008 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:02:54,013 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:02:54,014 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 15:02:54,028 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:02:54,036 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:03:54,456 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:03:54,461 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:03:54,466 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:03:54,478 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:03:54,480 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:03:54,485 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:03:54,489 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:03:54,495 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:03:54,496 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:03:54,519 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:03:54,539 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:03:54,545 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:03:54,559 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:03:54,561 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:01:18,268 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 16:01:18,274 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 16:01:18,285 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 16:01:18,287 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:01:18,299 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 16:01:18,300 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 16:01:18,303 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 16:01:18,315 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 16:01:18,318 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 16:01:18,326 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 16:01:18,336 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 16:01:18,343 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 16:01:18,346 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 16:01:18,351 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 16:02:18,586 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 16:02:18,589 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 16:02:18,590 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 16:02:18,609 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 16:02:18,611 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 16:02:18,615 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:02:18,620 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 16:02:18,623 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 16:02:18,634 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 16:02:18,649 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 16:02:18,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 16:02:18,673 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 16:02:18,676 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 16:02:18,678 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 16:05:19,853 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 16:05:19,903 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 16:05:19,928 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 16:05:19,945 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 16:05:19,954 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 16:05:19,978 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 17:01:43,335 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 17:01:43,345 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 17:01:43,347 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 17:01:43,356 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 17:01:43,367 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 17:01:43,369 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 17:01:43,376 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 17:01:43,378 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 17:01:43,380 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 17:01:43,394 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 17:01:43,406 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 17:01:43,408 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 17:01:43,418 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 17:01:43,432 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:23:58,628 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rental
2025-09-12 08:23:58,654 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_appointment_status because it was found in queue for rental
2025-09-12 08:23:58,665 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rental
2025-09-12 08:23:58,667 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.enqueue_auto_create_nhif_patient_claims because it was found in queue for rental
2025-09-12 08:23:58,670 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rental
2025-09-12 08:23:58,677 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-09-12 08:23:58,681 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rental
2025-09-12 08:23:58,683 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rental
2025-09-12 08:23:58,688 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-09-12 08:23:58,696 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rental
2025-09-12 08:23:58,699 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rental
2025-09-12 08:23:58,716 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rental
2025-09-12 08:23:58,725 ERROR scheduler Skipped queueing non_profit.non_profit.doctype.membership.membership.set_expired_status because it was found in queue for rental
2025-09-12 08:23:58,735 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rental
2025-09-12 08:23:58,744 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for rental
2025-09-12 08:23:58,747 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for rental
2025-09-12 08:23:58,750 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rental
2025-09-12 08:23:58,756 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.delete_or_cancel_draft_document because it was found in queue for rental
2025-09-12 08:23:58,763 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rental
2025-09-12 08:23:58,769 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rental
2025-09-12 08:23:58,773 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rental
2025-09-12 08:23:58,788 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rental
2025-09-12 08:23:58,794 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rental
2025-09-12 08:23:58,801 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rental
2025-09-12 08:23:58,818 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rental
2025-09-12 08:23:58,832 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rental
2025-09-12 08:23:58,840 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rental
2025-09-12 08:23:58,844 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rental
2025-09-12 08:23:58,850 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rental
2025-09-12 08:23:58,857 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rental
2025-09-12 08:23:58,864 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rental
2025-09-12 08:23:58,868 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rental
2025-09-12 08:23:58,870 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rental
2025-09-12 08:23:58,877 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rental
2025-09-12 08:23:58,883 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rental
2025-09-12 08:23:58,886 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rental
2025-09-12 08:23:58,890 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rental
2025-09-12 08:23:58,900 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rental
2025-09-12 08:23:58,903 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rental
2025-09-12 08:23:58,909 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rental
2025-09-12 08:23:58,913 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for rental
2025-09-12 08:23:58,918 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rental
2025-09-12 08:23:58,921 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rental
2025-09-12 08:23:58,926 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_tax_invoice.vfd_tax_invoice.posting_all_vfd_invoices_off_peak because it was found in queue for rental
2025-09-12 08:23:58,930 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rental
2025-09-12 08:23:58,935 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rental
2025-09-12 08:23:58,937 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rental
2025-09-12 08:23:58,940 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rental
2025-09-12 08:23:58,942 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rental
2025-09-12 08:23:58,945 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rental
2025-09-12 08:23:58,950 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rental
2025-09-12 08:23:58,955 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rental
2025-09-12 08:23:58,957 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rental
2025-09-12 08:23:58,960 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-09-12 08:23:58,963 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rental
2025-09-12 08:23:58,966 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for rental
2025-09-12 08:23:58,970 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rental
2025-09-12 08:23:58,973 ERROR scheduler Skipped queueing hms_tz.nhif.api.inpatient_record.daily_update_inpatient_occupancies because it was found in queue for rental
2025-09-12 08:23:58,975 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.api.sales_invoice.posting_all_vfd_invoices_off_peak because it was found in queue for rental
2025-09-12 08:23:58,978 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rental
2025-09-12 08:23:58,981 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rental
2025-09-12 08:23:58,983 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.api.sales_invoice.posting_all_vfd_invoices because it was found in queue for rental
2025-09-12 08:23:58,989 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rental
2025-09-12 08:23:58,992 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rental
2025-09-12 08:23:58,995 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-09-12 08:23:58,997 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rental
2025-09-12 08:23:59,000 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rental
2025-09-12 08:23:59,005 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for rental
2025-09-12 08:23:59,009 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rental
2025-09-12 08:23:59,012 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for rental
2025-09-12 08:23:59,016 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rental
2025-09-12 08:23:59,019 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for rental
2025-09-12 08:23:59,021 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rental
2025-09-12 08:23:59,026 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rental
2025-09-12 08:23:59,029 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rental
2025-09-12 08:23:59,032 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for rental
2025-09-12 08:23:59,034 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rental
2025-09-12 08:23:59,036 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rental
2025-09-12 08:23:59,038 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.fee_validity.fee_validity.update_validity_status because it was found in queue for rental
2025-09-12 08:23:59,041 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_z_report.vfd_z_report.send_multi_vfd_z_reports because it was found in queue for rental
2025-09-12 08:23:59,043 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rental
2025-09-12 08:23:59,046 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rental
2025-09-12 08:23:59,048 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rental
2025-09-12 08:23:59,050 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rental
2025-09-12 08:23:59,053 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for rental
2025-09-12 08:23:59,057 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rental
2025-09-12 08:23:59,059 ERROR scheduler Skipped queueing vfd_tz.api.utils.check_vfd_status because it was found in queue for rental
2025-09-12 08:23:59,064 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for rental
2025-09-12 08:23:59,066 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rental
2025-09-12 08:23:59,069 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rental
2025-09-12 08:23:59,073 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rental
2025-09-12 08:23:59,077 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rental
2025-09-12 08:23:59,080 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rental
2025-09-12 08:23:59,082 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rental
2025-09-12 08:23:59,085 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rental
2025-09-12 08:23:59,087 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rental
2025-09-12 08:23:59,089 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rental
2025-09-12 08:23:59,092 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rental
2025-09-12 08:23:59,094 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rental
2025-09-12 08:23:59,098 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rental
2025-09-12 08:23:59,100 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_tax_invoice.vfd_tax_invoice.posting_all_vfd_invoices because it was found in queue for rental
2025-09-12 08:23:59,102 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rental
2025-09-12 08:23:59,105 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rental
2025-09-12 08:23:59,107 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rental
2025-09-12 08:23:59,110 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for rental
2025-09-12 08:23:59,114 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-09-12 08:23:59,116 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_z_report.vfd_z_report.make_vfd_z_report because it was found in queue for rental
2025-09-12 08:23:59,118 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rental
2025-09-12 08:23:59,122 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rental
2025-09-12 08:23:59,128 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rental
2025-09-12 08:23:59,131 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rental
2025-09-12 08:23:59,132 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rental
2025-09-12 08:23:59,159 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:23:59,163 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:23:59,167 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:23:59,170 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:23:59,174 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:23:59,178 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:23:59,181 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:23:59,213 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:23:59,228 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:23:59,234 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:23:59,236 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:23:59,239 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:23:59,244 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:23:59,255 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:23:59,263 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:23:59,280 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:23:59,289 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:23:59,299 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:23:59,303 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:23:59,306 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:23:59,308 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:23:59,313 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:23:59,317 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:23:59,322 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:23:59,335 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:23:59,337 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:23:59,344 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:23:59,348 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:23:59,367 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:23:59,369 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:23:59,377 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:23:59,379 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:23:59,381 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:23:59,402 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-09-12 08:23:59,405 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:23:59,409 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-09-12 08:23:59,412 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:23:59,415 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rubis
2025-09-12 08:23:59,417 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-09-12 08:23:59,420 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for rubis
2025-09-12 08:23:59,425 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:23:59,428 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rubis
2025-09-12 08:23:59,430 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:23:59,434 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-09-12 08:23:59,437 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rubis
2025-09-12 08:23:59,439 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rubis
2025-09-12 08:23:59,442 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rubis
2025-09-12 08:23:59,453 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:23:59,457 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-09-12 08:23:59,462 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:23:59,464 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-09-12 08:23:59,466 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-09-12 08:23:59,468 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rubis
2025-09-12 08:23:59,472 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rubis
2025-09-12 08:23:59,474 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:23:59,477 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:23:59,480 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:23:59,482 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:23:59,485 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:23:59,488 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:23:59,491 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:23:59,495 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-09-12 08:23:59,498 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:23:59,500 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for rubis
2025-09-12 08:23:59,502 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:23:59,505 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:23:59,508 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rubis
2025-09-12 08:23:59,510 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rubis
2025-09-12 08:23:59,514 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rubis
2025-09-12 08:23:59,517 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-09-12 08:23:59,520 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:23:59,526 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rubis
2025-09-12 08:23:59,528 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rubis
2025-09-12 08:23:59,531 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rubis
2025-09-12 08:23:59,533 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:23:59,535 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-09-12 08:23:59,538 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rubis
2025-09-12 08:23:59,541 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for rubis
2025-09-12 08:23:59,544 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:23:59,549 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:23:59,551 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:23:59,553 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:23:59,556 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rubis
2025-09-12 08:23:59,558 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rubis
2025-09-12 08:23:59,561 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:23:59,564 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:23:59,567 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:23:59,569 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:23:59,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:23:59,574 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for rubis
2025-09-12 08:23:59,578 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-09-12 08:23:59,582 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rubis
2025-09-12 08:23:59,585 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:23:59,588 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rubis
2025-09-12 08:23:59,591 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:23:59,593 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-09-12 08:23:59,596 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-09-12 08:23:59,598 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:23:59,600 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rubis
2025-09-12 08:23:59,602 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:23:59,608 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rubis
2025-09-12 08:23:59,610 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-09-12 08:23:59,612 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-09-12 08:23:59,614 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:23:59,617 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for rubis
2025-09-12 08:23:59,619 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-09-12 08:23:59,622 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-09-12 08:23:59,624 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:23:59,626 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:23:59,628 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-09-12 08:23:59,630 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rubis
2025-09-12 08:23:59,632 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:23:59,634 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rubis
2025-09-12 08:23:59,636 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rubis
2025-09-12 08:23:59,639 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-09-12 08:23:59,643 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-09-12 08:23:59,646 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:23:59,648 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:23:59,650 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:23:59,652 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:23:59,654 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-09-12 08:23:59,656 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:23:59,658 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-09-12 08:23:59,661 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-09-12 08:23:59,665 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-09-12 08:23:59,666 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:23:59,669 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:23:59,671 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rubis
2025-09-12 08:23:59,675 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rubis
2025-09-12 08:23:59,677 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for rubis
2025-09-12 08:23:59,680 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rubis
2025-09-12 08:23:59,682 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:23:59,686 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:24:59,707 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:24:59,711 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:24:59,714 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:24:59,716 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:24:59,719 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:24:59,732 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:24:59,734 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:24:59,737 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:24:59,743 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:24:59,744 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:24:59,747 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:24:59,750 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:24:59,752 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:24:59,754 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:24:59,762 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:24:59,764 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:24:59,767 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:24:59,771 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:24:59,777 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:24:59,781 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:24:59,790 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:24:59,793 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:24:59,796 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:24:59,798 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:24:59,800 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:24:59,803 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:24:59,807 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:24:59,813 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:24:59,815 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:24:59,816 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:24:59,824 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:24:59,827 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:24:59,828 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:24:59,849 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-09-12 08:24:59,851 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-09-12 08:24:59,852 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-09-12 08:24:59,854 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rubis
2025-09-12 08:24:59,856 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-09-12 08:24:59,858 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rubis
2025-09-12 08:24:59,859 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rubis
2025-09-12 08:24:59,860 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-09-12 08:24:59,862 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rubis
2025-09-12 08:24:59,863 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-09-12 08:24:59,865 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-09-12 08:24:59,867 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-09-12 08:24:59,869 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for rubis
2025-09-12 08:24:59,870 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-09-12 08:24:59,872 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rubis
2025-09-12 08:24:59,874 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for rubis
2025-09-12 08:24:59,875 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rubis
2025-09-12 08:24:59,877 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-09-12 08:24:59,878 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-09-12 08:24:59,880 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-09-12 08:24:59,882 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-09-12 08:24:59,883 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-09-12 08:24:59,884 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rubis
2025-09-12 08:24:59,886 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-09-12 08:24:59,887 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:24:59,890 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rubis
2025-09-12 08:24:59,893 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-09-12 08:24:59,894 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-09-12 08:24:59,896 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-09-12 08:24:59,901 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rubis
2025-09-12 08:24:59,902 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-09-12 08:24:59,905 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:24:59,907 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rubis
2025-09-12 08:24:59,909 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-09-12 08:24:59,910 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-09-12 08:24:59,912 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rubis
2025-09-12 08:24:59,913 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-09-12 08:24:59,916 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-09-12 08:24:59,918 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-09-12 08:24:59,920 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-09-12 08:24:59,922 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-09-12 08:24:59,923 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-09-12 08:24:59,926 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rubis
2025-09-12 08:24:59,928 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rubis
2025-09-12 08:24:59,929 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rubis
2025-09-12 08:24:59,931 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rubis
2025-09-12 08:24:59,933 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-09-12 08:24:59,935 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-09-12 08:24:59,937 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-09-12 08:24:59,939 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-09-12 08:24:59,941 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:24:59,942 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rubis
2025-09-12 08:24:59,944 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-09-12 08:24:59,945 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rubis
2025-09-12 08:24:59,947 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-09-12 08:24:59,949 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-09-12 08:24:59,950 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-09-12 08:24:59,952 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-09-12 08:24:59,953 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rubis
2025-09-12 08:24:59,955 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-09-12 08:24:59,957 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-09-12 08:24:59,959 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-09-12 08:24:59,960 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for rubis
2025-09-12 08:24:59,962 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rubis
2025-09-12 08:24:59,964 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-09-12 08:24:59,967 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-09-12 08:24:59,969 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-09-12 08:24:59,970 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rubis
2025-09-12 08:24:59,971 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-09-12 08:24:59,973 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-09-12 08:24:59,975 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:24:59,976 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-09-12 08:24:59,977 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rubis
2025-09-12 08:24:59,979 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for rubis
2025-09-12 08:24:59,980 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-09-12 08:24:59,981 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rubis
2025-09-12 08:24:59,983 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rubis
2025-09-12 08:24:59,985 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-09-12 08:24:59,987 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rubis
2025-09-12 08:24:59,988 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-09-12 08:24:59,989 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-09-12 08:24:59,990 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-09-12 08:24:59,992 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-09-12 08:24:59,993 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rubis
2025-09-12 08:24:59,995 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-09-12 08:24:59,996 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rubis
2025-09-12 08:24:59,998 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for rubis
2025-09-12 08:25:00,000 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-09-12 08:25:00,001 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-09-12 08:25:00,003 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-09-12 08:25:00,005 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-09-12 08:25:00,006 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-09-12 08:25:00,008 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rubis
2025-09-12 08:25:00,009 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-09-12 08:25:00,010 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-09-12 08:25:00,011 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for rubis
2025-09-12 08:25:00,013 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-09-12 08:25:00,014 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rubis
2025-09-12 08:25:00,016 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rubis
2025-09-12 08:25:00,017 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-09-12 08:25:00,029 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rental
2025-09-12 08:25:00,030 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rental
2025-09-12 08:25:00,031 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for rental
2025-09-12 08:25:00,033 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rental
2025-09-12 08:25:00,035 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-09-12 08:25:00,037 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rental
2025-09-12 08:25:00,039 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rental
2025-09-12 08:25:00,040 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rental
2025-09-12 08:25:00,044 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for rental
2025-09-12 08:25:00,045 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rental
2025-09-12 08:25:00,047 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rental
2025-09-12 08:25:00,048 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rental
2025-09-12 08:25:00,049 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rental
2025-09-12 08:25:00,050 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.delete_or_cancel_draft_document because it was found in queue for rental
2025-09-12 08:25:00,052 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-09-12 08:25:00,053 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for rental
2025-09-12 08:25:00,058 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rental
2025-09-12 08:25:00,060 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.api.sales_invoice.posting_all_vfd_invoices because it was found in queue for rental
2025-09-12 08:25:00,061 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rental
2025-09-12 08:25:00,064 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rental
2025-09-12 08:25:00,069 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rental
2025-09-12 08:25:00,074 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for rental
2025-09-12 08:25:00,075 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rental
2025-09-12 08:25:00,076 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_z_report.vfd_z_report.send_multi_vfd_z_reports because it was found in queue for rental
2025-09-12 08:25:00,077 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rental
2025-09-12 08:25:00,079 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rental
2025-09-12 08:25:00,081 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rental
2025-09-12 08:25:00,082 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for rental
2025-09-12 08:25:00,084 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rental
2025-09-12 08:25:00,087 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rental
2025-09-12 08:25:00,089 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rental
2025-09-12 08:25:00,090 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rental
2025-09-12 08:25:00,092 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rental
2025-09-12 08:25:00,093 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rental
2025-09-12 08:25:00,095 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-09-12 08:25:00,096 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_tax_invoice.vfd_tax_invoice.posting_all_vfd_invoices because it was found in queue for rental
2025-09-12 08:25:00,098 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rental
2025-09-12 08:25:00,101 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rental
2025-09-12 08:25:00,103 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rental
2025-09-12 08:25:00,104 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rental
2025-09-12 08:25:00,105 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rental
2025-09-12 08:25:00,107 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rental
2025-09-12 08:25:00,108 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rental
2025-09-12 08:25:00,112 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for rental
2025-09-12 08:25:00,115 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rental
2025-09-12 08:25:00,117 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rental
2025-09-12 08:25:00,118 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rental
2025-09-12 08:25:00,120 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-09-12 08:25:00,122 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rental
2025-09-12 08:25:00,124 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rental
2025-09-12 08:25:00,126 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rental
2025-09-12 08:25:00,127 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rental
2025-09-12 08:25:00,130 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rental
2025-09-12 08:25:00,131 ERROR scheduler Skipped queueing hms_tz.nhif.api.inpatient_record.daily_update_inpatient_occupancies because it was found in queue for rental
